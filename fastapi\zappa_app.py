"""
Zappa-optimized entry point for ReplyPal FastAPI application.
This module provides a simple Flask app for testing Zappa deployment.
"""

import os
import sys
from pathlib import Path
from flask import Flask, jsonify

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Create a simple Flask app for testing Zappa deployment
app = Flask(__name__)

@app.route("/")
def root():
    """Root endpoint for health check."""
    return jsonify({
        "status": "ok",
        "message": "ReplyPal API is running",
        "version": os.getenv("APP_VERSION", "1.0.0")
    })

@app.route("/ping")
def ping():
    """Ping endpoint for health check."""
    return jsonify({"status": "ok", "message": "pong"})

@app.route("/test")
def test():
    """Test endpoint."""
    return jsonify({
        "status": "ok",
        "message": "Test endpoint working",
        "environment": os.getenv("ENVIRONMENT", "unknown")
    })

# TODO: Switch back to FastAPI once basic deployment is working
# from fastapi import FastAPI
# from mangum import Mangum
# fastapi_app = FastAPI(...)
# app = Mangum(fastapi_app)
